# 🗺️ 微信小程序高德静态地图完整配置指南

## 📋 必需配置操作清单

### 1. **API密钥配置**（必须）

#### 1.1 申请正确的API密钥
- 访问 [高德开放平台控制台](https://console.amap.com/dev/key/app)
- 创建应用，**平台类型必须选择"Web服务API"**
- 获取32位API密钥

#### 1.2 验证当前密钥
当前使用的密钥：`fa8516b9618f8fc794edbb1c26893bc2`

**重要提示：** 如果该密钥不是Web服务API类型，需要重新申请！

### 2. **微信小程序域名配置**（必须）

在微信公众平台配置以下域名：
- **request合法域名：** `https://restapi.amap.com`

#### 配置步骤：
1. 登录 [微信公众平台](https://mp.weixin.qq.com)
2. 进入"开发" → "开发管理" → "开发设置"
3. 在"服务器域名"中添加request合法域名
4. 保存配置

### 3. **API参数规范**（重要）

根据官方文档，静态地图API参数规范：

#### 必填参数：
- `key`: API密钥
- `zoom`: 地图级别 [1-17]
- `size`: 图片大小，格式如 `400*400`，最大 `1024*1024`

#### 可选参数：
- `location`: 中心点坐标，格式 `经度,纬度`
- `scale`: 1=普通图，2=高清图
- `markers`: 标注点，格式 `size,color,label:经度,纬度`
- `labels`: 标签
- `paths`: 折线/多边形
- `traffic`: 0=不显示路况，1=显示路况

## 🔧 技术实现

### API调用示例

```javascript
// 标准静态地图URL构建
const staticMapUrl = 'https://restapi.amap.com/v3/staticmap?' + 
  'key=fa8516b9618f8fc794edbb1c26893bc2&' +
  'location=116.397428,39.90923&' +
  'zoom=15&' +
  'size=400*400&' +
  'scale=2&' +
  'markers=mid,0xFF0000,A:116.397428,39.90923';
```

### 微信小程序中的实现

```javascript
// 在微信小程序中显示静态地图
Page({
  data: {
    staticMapUrl: ''
  },
  
  onLoad: function() {
    this.loadStaticMap();
  },
  
  loadStaticMap: function() {
    const mapUrl = this.buildMapUrl();
    this.setData({
      staticMapUrl: mapUrl
    });
  },
  
  buildMapUrl: function() {
    const params = {
      key: 'fa8516b9618f8fc794edbb1c26893bc2',
      location: '116.397428,39.90923',
      zoom: 15,
      size: '400*400',
      scale: 2,
      markers: 'mid,0xFF0000,A:116.397428,39.90923'
    };
    
    const queryString = Object.entries(params)
      .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
      .join('&');
      
    return `https://restapi.amap.com/v3/staticmap?${queryString}`;
  }
});
```

### WXML模板

```xml
<!-- 静态地图显示 -->
<image 
  class="static-map" 
  src="{{staticMapUrl}}" 
  mode="aspectFit"
  bindload="onMapLoad"
  binderror="onMapError">
</image>
```

## 🧪 测试验证

### 1. 使用小程序内置测试
- 打开小程序首页
- 点击"🛠️ 配置指南"按钮
- 执行"测试API连接"和"测试静态地图"

### 2. 浏览器测试
打开项目根目录下的 `test_static_map.html` 文件进行测试

### 3. 手动URL测试
在浏览器中直接访问：
```
https://restapi.amap.com/v3/staticmap?key=fa8516b9618f8fc794edbb1c26893bc2&location=116.397428,39.90923&zoom=15&size=400*400&scale=2&markers=mid,0xFF0000,A:116.397428,39.90923
```

## ❌ 常见错误及解决方案

### 1. API密钥错误
**错误信息：** `USERKEY_PLAT_NOMATCH`
**解决方案：** 确保API密钥是Web服务API类型，不是其他平台类型

### 2. 域名未配置
**错误信息：** 网络请求失败
**解决方案：** 在微信公众平台配置 `https://restapi.amap.com` 域名

### 3. 参数格式错误
**错误信息：** 返回错误信息而非图片
**解决方案：** 检查参数格式，特别是坐标和尺寸格式

### 4. 图片加载失败
**解决方案：** 
- 检查网络连接
- 验证URL格式
- 确认API调用次数未超限

## 📱 项目文件结构

```
├── pages/
│   ├── index/              # 首页（显示静态地图）
│   ├── setup/              # 配置指南页面
│   ├── config/             # API配置说明页面
│   └── location/           # 动态地图页面
├── utils/
│   ├── staticMapConfig.js  # 静态地图配置工具
│   ├── staticMapTest.js    # 静态地图测试工具
│   └── config.js           # 统一配置文件
├── test_static_map.html    # 浏览器测试页面
└── STATIC_MAP_GUIDE.md     # 本配置指南
```

## 🎯 功能特性

- ✅ 自动获取当前位置生成静态地图
- ✅ 支持多种地图样式和标记点
- ✅ 完整的错误处理和状态提示
- ✅ 配置检查和测试工具
- ✅ 详细的配置指南和文档

## 📞 技术支持

如遇问题，请按以下顺序排查：

1. **检查API密钥**：确保是Web服务API类型
2. **验证域名配置**：确认已在微信公众平台配置
3. **测试网络连接**：使用配置指南页面的测试功能
4. **查看控制台日志**：检查具体错误信息
5. **参考官方文档**：[高德静态地图API文档](https://lbs.amap.com/api/webservice/guide/api/staticmaps)

---

**最后更新：** 2025年6月26日  
**版本：** 1.0.0
