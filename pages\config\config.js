// config.js
const CONFIG = require('../../utils/config.js');
const mapTest = require('../../utils/mapTest.js');

Page({
  data: {
    apiKey: CONFIG.AMAP_KEY,
    errorMsg: '',
    testResult: null
  },

  onLoad: function() {
    // 页面加载时检查API状态
    this.checkApiStatus();
  },

  // 检查API状态
  checkApiStatus: function() {
    const that = this;
    
    mapTest.testAmapConnection()
      .then(result => {
        that.setData({
          errorMsg: '',
          testResult: {
            success: true,
            message: 'API连接正常',
            detail: '高德地图服务可以正常使用'
          }
        });
      })
      .catch(error => {
        that.setData({
          errorMsg: error.message,
          testResult: {
            success: false,
            message: 'API连接失败',
            detail: error.message
          }
        });
      });
  },

  // 复制API密钥
  copyKey: function() {
    wx.setClipboardData({
      data: this.data.apiKey,
      success: function() {
        wx.showToast({
          title: 'API密钥已复制',
          icon: 'success'
        });
      }
    });
  },

  // 复制域名
  copyDomain: function(e) {
    const domain = e.currentTarget.dataset.domain;
    wx.setClipboardData({
      data: domain,
      success: function() {
        wx.showToast({
          title: '域名已复制',
          icon: 'success'
        });
      }
    });
  },

  // 测试API连接
  testConnection: function() {
    const that = this;
    
    wx.showLoading({
      title: '测试中...'
    });

    mapTest.testAmapConnection()
      .then(result => {
        wx.hideLoading();
        that.setData({
          testResult: {
            success: true,
            message: '✅ API连接测试成功',
            detail: `服务正常，可以获取天气等信息`
          }
        });
        
        wx.showToast({
          title: 'API连接正常',
          icon: 'success'
        });
      })
      .catch(error => {
        wx.hideLoading();
        that.setData({
          testResult: {
            success: false,
            message: '❌ API连接测试失败',
            detail: error.message
          }
        });
        
        wx.showToast({
          title: 'API连接失败',
          icon: 'none'
        });
      });
  },

  // 测试定位服务
  testLocation: function() {
    const that = this;
    
    wx.showLoading({
      title: '测试定位中...'
    });

    mapTest.testLocationService()
      .then(result => {
        wx.hideLoading();
        that.setData({
          testResult: {
            success: true,
            message: '✅ 定位服务测试成功',
            detail: `经度: ${result.data.longitude.toFixed(6)}\n纬度: ${result.data.latitude.toFixed(6)}\n精度: ${result.data.accuracy}米`
          }
        });
        
        wx.showToast({
          title: '定位服务正常',
          icon: 'success'
        });
      })
      .catch(error => {
        wx.hideLoading();
        that.setData({
          testResult: {
            success: false,
            message: '❌ 定位服务测试失败',
            detail: error.message
          }
        });
        
        wx.showModal({
          title: '定位权限',
          content: '定位失败，可能需要授权位置权限。是否前往设置？',
          success: function(res) {
            if (res.confirm) {
              wx.openSetting({
                success: function(settingRes) {
                  if (settingRes.authSetting['scope.userLocation']) {
                    wx.showToast({
                      title: '权限已开启',
                      icon: 'success'
                    });
                  }
                }
              });
            }
          }
        });
      });
  },

  // 返回首页
  goBack: function() {
    wx.navigateBack({
      delta: 1
    });
  },

  // 页面分享
  onShareAppMessage: function() {
    return {
      title: '高德地图API配置说明',
      path: '/pages/config/config'
    };
  }
});
