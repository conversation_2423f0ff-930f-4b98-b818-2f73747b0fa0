// mapTest.js - 高德地图API测试工具
const AMapWX = require('../libs/amap-wx.130.js');
const CONFIG = require('./config.js');

// 测试高德地图API连接
function testAmapConnection() {
  return new Promise((resolve, reject) => {
    try {
      const amapPlugin = new AMapWX.AMapWX({
        key: CONFIG.AMAP_KEY
      });
      
      console.log('高德地图SDK初始化成功');
      console.log('API Key:', CONFIG.AMAP_KEY);
      
      // 测试获取天气信息（不需要定位权限）
      amapPlugin.getWeather({
        city: '北京',
        success: function(data) {
          console.log('API连接测试成功:', data);
          resolve({
            success: true,
            message: 'API连接正常',
            data: data
          });
        },
        fail: function(error) {
          console.error('API连接测试失败:', error);

          let errorMessage = 'API连接失败';
          if (error.errMsg && error.errMsg.includes('USERKEY_PLAT_NOMATCH')) {
            errorMessage = 'API密钥与平台不匹配，请检查密钥配置';
          } else if (error.errMsg && error.errMsg.includes('INVALID_USER_KEY')) {
            errorMessage = 'API密钥无效，请检查密钥是否正确';
          } else if (error.errMsg && error.errMsg.includes('DAILY_QUERY_OVER_LIMIT')) {
            errorMessage = 'API调用次数超限，请稍后再试';
          }

          reject({
            success: false,
            message: errorMessage,
            error: error
          });
        }
      });
      
    } catch (error) {
      console.error('SDK初始化失败:', error);
      reject({
        success: false,
        message: 'SDK初始化失败',
        error: error
      });
    }
  });
}

// 测试定位功能
function testLocationService() {
  return new Promise((resolve, reject) => {
    wx.getLocation({
      type: 'gcj02',
      success: function(res) {
        console.log('微信定位成功:', res);
        resolve({
          success: true,
          message: '定位服务正常',
          data: {
            longitude: res.longitude,
            latitude: res.latitude,
            accuracy: res.accuracy
          }
        });
      },
      fail: function(error) {
        console.error('微信定位失败:', error);
        reject({
          success: false,
          message: '定位服务失败',
          error: error
        });
      }
    });
  });
}

module.exports = {
  testAmapConnection,
  testLocationService
};
