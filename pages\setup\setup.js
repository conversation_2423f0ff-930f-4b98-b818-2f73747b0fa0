// setup.js
const CONFIG = require('../../utils/config.js');
const staticMapConfig = require('../../utils/staticMapConfig.js');

Page({
  data: {
    apiKeyStatus: { success: false, message: '检查中...' },
    domainStatus: { success: false, message: '未知' },
    mapTestStatus: { success: false, message: '未测试' },
    exampleUrl: '',
    testResult: null
  },

  onLoad: function() {
    this.initializeData();
    this.checkConfiguration();
  },

  // 初始化数据
  initializeData: function() {
    const exampleUrl = staticMapConfig.buildStandardStaticMapUrl({
      location: staticMapConfig.STATIC_MAP_CONFIG.LOCATIONS.BEIJING_TIANANMEN,
      zoom: 10,
      size: '400*400',
      scale: 2,
      markers: staticMapConfig.STATIC_MAP_CONFIG.MARKER_STYLES.RED_A + ':' + staticMapConfig.STATIC_MAP_CONFIG.LOCATIONS.BEIJING_TIANANMEN
    });
    
    this.setData({
      exampleUrl: exampleUrl
    });
  },

  // 检查配置状态
  checkConfiguration: function() {
    this.checkApiKey();
    this.checkDomainConfig();
  },

  // 检查API密钥
  checkApiKey: function() {
    const that = this;
    
    // 检查密钥格式
    if (!CONFIG.AMAP_KEY || CONFIG.AMAP_KEY.length !== 32) {
      that.setData({
        'apiKeyStatus.success': false,
        'apiKeyStatus.message': 'API密钥格式错误'
      });
      return;
    }
    
    // 测试API密钥有效性
    const testUrl = staticMapConfig.buildStandardStaticMapUrl({
      location: '116.397428,39.90923',
      zoom: 10,
      size: '100*100'
    });
    
    staticMapConfig.testStaticMapUrl(testUrl)
      .then(result => {
        that.setData({
          'apiKeyStatus.success': true,
          'apiKeyStatus.message': 'API密钥有效'
        });
      })
      .catch(error => {
        that.setData({
          'apiKeyStatus.success': false,
          'apiKeyStatus.message': 'API密钥无效或网络错误'
        });
      });
  },

  // 检查域名配置
  checkDomainConfig: function() {
    // 这里只能提示用户检查，无法自动检测
    this.setData({
      'domainStatus.success': true,
      'domainStatus.message': '请手动确认已配置'
    });
  },

  // 打开高德控制台
  openAmapConsole: function() {
    wx.setClipboardData({
      data: 'https://console.amap.com/dev/key/app',
      success: function() {
        wx.showModal({
          title: '提示',
          content: '高德开放平台控制台地址已复制到剪贴板，请在浏览器中打开',
          showCancel: false
        });
      }
    });
  },

  // 复制当前密钥
  copyCurrentKey: function() {
    wx.setClipboardData({
      data: CONFIG.AMAP_KEY,
      success: function() {
        wx.showToast({
          title: '密钥已复制',
          icon: 'success'
        });
      }
    });
  },

  // 复制域名
  copyDomain: function() {
    wx.setClipboardData({
      data: 'https://restapi.amap.com',
      success: function() {
        wx.showToast({
          title: '域名已复制',
          icon: 'success'
        });
      }
    });
  },

  // 打开微信公众平台
  openWechatPlatform: function() {
    wx.setClipboardData({
      data: 'https://mp.weixin.qq.com',
      success: function() {
        wx.showModal({
          title: '提示',
          content: '微信公众平台地址已复制到剪贴板，请在浏览器中打开并配置域名白名单',
          showCancel: false
        });
      }
    });
  },

  // 测试API连接
  testApiConnection: function() {
    const that = this;
    
    wx.showLoading({
      title: '测试中...'
    });

    const testUrl = staticMapConfig.buildStandardStaticMapUrl({
      location: staticMapConfig.STATIC_MAP_CONFIG.LOCATIONS.BEIJING_TIANANMEN,
      zoom: 10,
      size: '200*200'
    });

    staticMapConfig.testStaticMapUrl(testUrl)
      .then(result => {
        wx.hideLoading();
        
        that.setData({
          testResult: {
            success: true,
            message: '✅ API连接测试成功',
            detail: `内容类型: ${result.contentType}\nURL有效，可以正常获取静态地图`
          },
          'apiKeyStatus.success': true,
          'apiKeyStatus.message': 'API密钥有效',
          'mapTestStatus.success': true,
          'mapTestStatus.message': '测试通过'
        });
        
        wx.showToast({
          title: 'API连接正常',
          icon: 'success'
        });
      })
      .catch(error => {
        wx.hideLoading();
        
        that.setData({
          testResult: {
            success: false,
            message: '❌ API连接测试失败',
            detail: `错误信息: ${error.message}\n请检查API密钥和域名配置`
          },
          'apiKeyStatus.success': false,
          'apiKeyStatus.message': 'API密钥可能无效',
          'mapTestStatus.success': false,
          'mapTestStatus.message': '测试失败'
        });
        
        wx.showToast({
          title: 'API连接失败',
          icon: 'none'
        });
      });
  },

  // 测试静态地图
  testStaticMap: function() {
    const that = this;
    
    wx.showLoading({
      title: '测试静态地图...'
    });

    staticMapConfig.getCurrentLocationMap()
      .then(result => {
        wx.hideLoading();
        
        that.setData({
          testResult: {
            success: true,
            message: '✅ 静态地图测试成功',
            detail: `地图URL已生成\n位置: ${result.location.longitude}, ${result.location.latitude}\n${result.message}`
          },
          'mapTestStatus.success': true,
          'mapTestStatus.message': '静态地图正常'
        });
        
        wx.showToast({
          title: '静态地图正常',
          icon: 'success'
        });
      })
      .catch(error => {
        wx.hideLoading();
        
        that.setData({
          testResult: {
            success: false,
            message: '❌ 静态地图测试失败',
            detail: `错误信息: ${error.message}`
          },
          'mapTestStatus.success': false,
          'mapTestStatus.message': '静态地图异常'
        });
        
        wx.showToast({
          title: '静态地图异常',
          icon: 'none'
        });
      });
  },

  // 复制示例URL
  copyExampleUrl: function() {
    wx.setClipboardData({
      data: this.data.exampleUrl,
      success: function() {
        wx.showToast({
          title: '示例URL已复制',
          icon: 'success'
        });
      }
    });
  },

  // 刷新状态
  refreshStatus: function() {
    this.setData({
      apiKeyStatus: { success: false, message: '检查中...' },
      domainStatus: { success: false, message: '检查中...' },
      mapTestStatus: { success: false, message: '检查中...' },
      testResult: null
    });
    
    this.checkConfiguration();
    
    wx.showToast({
      title: '状态已刷新',
      icon: 'success'
    });
  },

  // 返回首页
  goBack: function() {
    wx.navigateBack({
      delta: 1
    });
  },

  // 页面分享
  onShareAppMessage: function() {
    return {
      title: '高德静态地图配置指南',
      path: '/pages/setup/setup'
    };
  }
});
