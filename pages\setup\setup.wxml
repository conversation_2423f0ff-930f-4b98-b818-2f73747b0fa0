<!--setup.wxml-->
<view class="container">
  <view class="header">
    <text class="title">🛠️ 静态地图配置指南</text>
    <text class="subtitle">按照以下步骤配置高德静态地图API</text>
  </view>
  
  <!-- 配置检查状态 -->
  <view class="status-section">
    <view class="section-title">📊 配置状态检查</view>
    
    <view class="status-item">
      <view class="status-label">API密钥状态</view>
      <view class="status-value {{apiKeyStatus.success ? 'success' : 'error'}}">
        {{apiKeyStatus.message}}
      </view>
    </view>
    
    <view class="status-item">
      <view class="status-label">域名配置状态</view>
      <view class="status-value {{domainStatus.success ? 'success' : 'warning'}}">
        {{domainStatus.message}}
      </view>
    </view>
    
    <view class="status-item">
      <view class="status-label">静态地图测试</view>
      <view class="status-value {{mapTestStatus.success ? 'success' : 'error'}}">
        {{mapTestStatus.message}}
      </view>
    </view>
  </view>
  
  <!-- 配置步骤 -->
  <view class="steps-section">
    <view class="section-title">📋 配置步骤</view>
    
    <view class="step-item">
      <view class="step-number">1</view>
      <view class="step-content">
        <view class="step-title">申请Web服务API密钥</view>
        <view class="step-desc">访问高德开放平台控制台，创建应用并选择"Web服务API"类型</view>
        <view class="step-action">
          <button class="action-btn" bindtap="openAmapConsole">打开控制台</button>
          <button class="action-btn secondary" bindtap="copyCurrentKey">复制当前密钥</button>
        </view>
      </view>
    </view>
    
    <view class="step-item">
      <view class="step-number">2</view>
      <view class="step-content">
        <view class="step-title">配置微信小程序域名</view>
        <view class="step-desc">在微信公众平台配置request合法域名</view>
        <view class="domain-box">
          <text class="domain-text">https://restapi.amap.com</text>
          <button class="copy-btn" bindtap="copyDomain">复制</button>
        </view>
        <view class="step-action">
          <button class="action-btn" bindtap="openWechatPlatform">打开微信公众平台</button>
        </view>
      </view>
    </view>
    
    <view class="step-item">
      <view class="step-number">3</view>
      <view class="step-content">
        <view class="step-title">测试API连接</view>
        <view class="step-desc">验证API密钥和域名配置是否正确</view>
        <view class="step-action">
          <button class="action-btn" bindtap="testApiConnection">测试连接</button>
          <button class="action-btn secondary" bindtap="testStaticMap">测试静态地图</button>
        </view>
      </view>
    </view>
  </view>
  
  <!-- API参数说明 -->
  <view class="params-section">
    <view class="section-title">📖 API参数说明</view>
    
    <view class="param-group">
      <view class="param-title">必填参数</view>
      <view class="param-item">
        <text class="param-name">key</text>
        <text class="param-desc">API密钥</text>
      </view>
      <view class="param-item">
        <text class="param-name">zoom</text>
        <text class="param-desc">地图级别 [1-17]</text>
      </view>
      <view class="param-item">
        <text class="param-name">size</text>
        <text class="param-desc">图片大小，如400*400</text>
      </view>
    </view>
    
    <view class="param-group">
      <view class="param-title">可选参数</view>
      <view class="param-item">
        <text class="param-name">location</text>
        <text class="param-desc">中心点坐标</text>
      </view>
      <view class="param-item">
        <text class="param-name">scale</text>
        <text class="param-desc">1:普通图 2:高清图</text>
      </view>
      <view class="param-item">
        <text class="param-name">markers</text>
        <text class="param-desc">标注点</text>
      </view>
      <view class="param-item">
        <text class="param-name">traffic</text>
        <text class="param-desc">0:无路况 1:显示路况</text>
      </view>
    </view>
  </view>
  
  <!-- 示例URL -->
  <view class="example-section">
    <view class="section-title">🌐 示例URL</view>
    <view class="example-url">
      <text class="url-text">{{exampleUrl}}</text>
      <button class="copy-btn" bindtap="copyExampleUrl">复制</button>
    </view>
  </view>
  
  <!-- 测试结果 -->
  <view class="result-section" wx:if="{{testResult}}">
    <view class="section-title">🔍 测试结果</view>
    <view class="result-content {{testResult.success ? 'success' : 'error'}}">
      <text class="result-text">{{testResult.message}}</text>
      <view wx:if="{{testResult.detail}}" class="result-detail">
        <text class="detail-text">{{testResult.detail}}</text>
      </view>
    </view>
  </view>
  
  <!-- 底部按钮 -->
  <view class="footer">
    <button class="footer-btn" bindtap="goBack">返回首页</button>
    <button class="footer-btn primary" bindtap="refreshStatus">刷新状态</button>
  </view>
</view>
