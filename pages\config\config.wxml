<!--config.wxml-->
<view class="container">
  <view class="header">
    <text class="title">高德地图API配置说明</text>
  </view>
  
  <view class="content">
    <view class="section">
      <view class="section-title">🔑 当前API密钥</view>
      <view class="key-display">
        <text class="key-text">{{apiKey}}</text>
        <button class="copy-btn" bindtap="copyKey">复制</button>
      </view>
    </view>
    
    <view class="section">
      <view class="section-title">⚠️ 错误信息</view>
      <view class="error-info" wx:if="{{errorMsg}}">
        <text class="error-text">{{errorMsg}}</text>
      </view>
      <view class="no-error" wx:else>
        <text class="success-text">✅ 暂无错误</text>
      </view>
    </view>
    
    <view class="section">
      <view class="section-title">🛠️ 配置步骤</view>
      <view class="steps">
        <view class="step">
          <text class="step-number">1</text>
          <text class="step-text">访问高德开放平台控制台</text>
        </view>
        <view class="step">
          <text class="step-number">2</text>
          <text class="step-text">创建应用并选择"微信小程序"平台</text>
        </view>
        <view class="step">
          <text class="step-number">3</text>
          <text class="step-text">获取API Key并替换配置文件中的密钥</text>
        </view>
        <view class="step">
          <text class="step-number">4</text>
          <text class="step-text">在微信公众平台配置域名白名单</text>
        </view>
      </view>
    </view>
    
    <view class="section">
      <view class="section-title">🌐 域名配置</view>
      <view class="domain-list">
        <view class="domain-item">
          <text class="domain-text">https://restapi.amap.com</text>
          <button class="copy-btn" bindtap="copyDomain" data-domain="https://restapi.amap.com">复制</button>
        </view>
      </view>
    </view>
    
    <view class="section">
      <view class="section-title">🔧 测试功能</view>
      <view class="test-buttons">
        <button class="test-btn" bindtap="testConnection">测试API连接</button>
        <button class="test-btn" bindtap="testLocation">测试定位服务</button>
      </view>
    </view>
    
    <view class="section">
      <view class="section-title">📋 测试结果</view>
      <view class="test-result" wx:if="{{testResult}}">
        <text class="result-text {{testResult.success ? 'success' : 'error'}}">
          {{testResult.message}}
        </text>
        <view class="result-detail" wx:if="{{testResult.detail}}">
          <text class="detail-text">{{testResult.detail}}</text>
        </view>
      </view>
    </view>
  </view>
  
  <view class="footer">
    <button class="back-btn" bindtap="goBack">返回首页</button>
  </view>
</view>
