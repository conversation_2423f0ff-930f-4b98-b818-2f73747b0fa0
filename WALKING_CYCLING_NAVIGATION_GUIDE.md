# 🚶🚴 微信小程序步行骑行导航功能指南

## 📋 基于高德Android导航SDK的设计思路

### 🔍 **核心设计理念**
参考高德Android导航SDK的步行和骑行路线规划功能，在微信小程序中实现类似的导航体验：

1. **起点自动获取**：自动获取用户当前位置作为起点
2. **终点键盘输入**：支持地址搜索和POI选择
3. **专注步行骑行**：优化步行和骑行路线算法
4. **多路线选择**：提供单路线和多路线规划选项

## 🛠️ **功能实现方案**

### **1. 起点自动获取**
```javascript
// 自动获取当前位置作为起点
getCurrentLocation: function() {
  wx.getLocation({
    type: 'gcj02',
    success: function(res) {
      // 获取坐标
      const location = {
        longitude: res.longitude,
        latitude: res.latitude
      };
      
      // 逆地理编码获取详细地址
      poiSearchConfig.reverseGeocoding(longitude, latitude)
        .then(result => {
          // 显示详细地址
          this.setData({
            currentLocationAddress: result.formatted_address
          });
        });
    }
  });
}
```

### **2. 终点键盘输入搜索**
```javascript
// POI搜索功能
performPOISearch: function(keyword) {
  const searchOptions = {
    location: `${currentLocation.longitude},${currentLocation.latitude}`,
    radius: 50000,     // 50km搜索范围
    sortrule: 'distance' // 按距离排序
  };
  
  poiSearchConfig.searchPOI(keyword, searchOptions)
    .then(result => {
      // 显示搜索结果列表
      this.setData({
        searchResults: result.results
      });
    });
}
```

### **3. 路线规划策略**
```javascript
// 步行和骑行路线规划
const routeTypes = [
  { key: 'walking', name: '🚶 步行', strategy: 'SINGLE' },
  { key: 'bicycling', name: '🚴 骑行', strategy: 'SINGLE' },
  { key: 'walking_multi', name: '🚶 步行(多路线)', strategy: 'MULTIPLE' },
  { key: 'bicycling_multi', name: '🚴 骑行(多路线)', strategy: 'MULTIPLE' }
];

// 构建路线规划选项
buildRouteOptions: function(routeType, strategy) {
  const options = {};
  
  switch (routeType) {
    case 'walking':
      options.alternative_route = strategy === 'MULTIPLE' ? 1 : 0;
      break;
    case 'bicycling':
      options.alternative_route = strategy === 'MULTIPLE' ? 1 : 0;
      break;
  }
  
  return options;
}
```

## 🎯 **用户界面设计**

### **起点显示区域**
```xml
<!-- 起点（自动获取当前位置） -->
<view class="input-row start-row">
  <view class="location-icon start-icon">🟢</view>
  <view class="location-info">
    <view class="location-label">起点（当前位置）</view>
    <view class="location-address">{{currentLocationAddress}}</view>
  </view>
  <button class="location-action-btn" bindtap="refreshCurrentLocation">🔄</button>
</view>
```

### **终点输入区域**
```xml
<!-- 终点（键盘输入） -->
<view class="input-row end-row">
  <view class="location-icon end-icon">🔴</view>
  <view class="destination-input-container">
    <input 
      class="destination-input" 
      placeholder="请输入目的地地址或关键词" 
      value="{{destinationInput}}"
      bindinput="onDestinationInput"
      bindconfirm="searchDestination"
      confirm-type="search"
    />
    <button class="search-btn" bindtap="searchDestination">搜索</button>
  </view>
</view>
```

### **搜索结果列表**
```xml
<!-- 搜索结果列表 -->
<view class="search-results" wx:if="{{searchResults.length > 0}}">
  <scroll-view class="results-scroll" scroll-y="true">
    <view 
      class="result-item" 
      wx:for="{{searchResults}}" 
      wx:key="id"
      bindtap="selectDestination"
      data-index="{{index}}">
      <view class="result-name">{{item.name}}</view>
      <view class="result-address">{{item.address}}</view>
      <view class="result-distance">距离: {{item.distance}}米</view>
    </view>
  </scroll-view>
</view>
```

## 🔧 **技术实现细节**

### **1. POI搜索配置**
```javascript
// POI搜索API配置
const POI_SEARCH_CONFIG = {
  BASE_URL: 'https://restapi.amap.com/v3/place/text',
  GEOCODING_URL: 'https://restapi.amap.com/v3/geocode/geo',
  REVERSE_GEOCODING_URL: 'https://restapi.amap.com/v3/geocode/regeo',
  
  DEFAULTS: {
    city: '全国',
    output: 'json',
    offset: 10,
    page: 1,
    extensions: 'all'
  }
};
```

### **2. 路线规划API调用**
```javascript
// 步行路线规划
const walkingUrl = 'https://restapi.amap.com/v3/direction/walking?' +
  'key=API_KEY&' +
  'origin=起点经纬度&' +
  'destination=终点经纬度&' +
  'alternative_route=0'; // 0:单路线 1:多路线

// 骑行路线规划
const bicyclingUrl = 'https://restapi.amap.com/v4/direction/bicycling?' +
  'key=API_KEY&' +
  'origin=起点经纬度&' +
  'destination=终点经纬度&' +
  'alternative_route=0'; // 0:单路线 1:多路线
```

### **3. 地图显示优化**
```javascript
// 地图标记点配置
updateMarkers: function() {
  const markers = [
    {
      id: 1,
      latitude: currentLocation.latitude,
      longitude: currentLocation.longitude,
      title: '当前位置（起点）',
      callout: {
        content: '起点',
        bgColor: '#4caf50'
      }
    },
    {
      id: 2,
      latitude: selectedDestination.latitude,
      longitude: selectedDestination.longitude,
      title: selectedDestination.name,
      callout: {
        content: '终点',
        bgColor: '#f44336'
      }
    }
  ];
}
```

## 📱 **用户体验优化**

### **1. 智能搜索**
- **距离排序**：优先显示距离较近的POI
- **搜索范围**：默认50km搜索范围
- **实时搜索**：输入时实时显示搜索建议

### **2. 位置管理**
- **自动定位**：页面加载时自动获取当前位置
- **位置刷新**：支持手动刷新当前位置
- **地址显示**：显示详细的地址信息

### **3. 路线选择**
- **多种模式**：步行、骑行单路线和多路线选择
- **路线对比**：多路线模式下可对比不同路线
- **智能推荐**：根据距离推荐最佳路线

## 🎨 **视觉设计**

### **颜色方案**
- **起点**：绿色系（#4caf50）
- **终点**：红色系（#f44336）
- **路线**：蓝色系（#2196f3）
- **搜索**：橙色系（#ff9800）

### **交互反馈**
- **加载状态**：搜索时显示加载动画
- **成功提示**：操作成功时显示Toast提示
- **错误处理**：网络错误时显示重试选项

## 🚀 **使用流程**

### **基本导航流程**
1. **自动定位**：打开页面自动获取当前位置
2. **输入目的地**：在搜索框输入目的地关键词
3. **选择地点**：从搜索结果中选择目标地点
4. **选择方式**：选择步行或骑行导航方式
5. **规划路线**：点击开始路线规划
6. **查看路线**：在地图上查看规划的路线

### **高级功能**
- **多路线对比**：选择多路线模式查看不同路线选项
- **位置刷新**：手动刷新当前位置
- **搜索历史**：记录常用搜索地点

## 📊 **性能优化**

### **搜索优化**
- **防抖处理**：避免频繁搜索请求
- **结果缓存**：缓存搜索结果减少重复请求
- **分页加载**：大量结果时分页显示

### **地图优化**
- **视野调整**：自动调整地图视野包含起终点
- **标记管理**：合理控制地图标记数量
- **路线绘制**：优化路线绘制性能

## ✅ **配置要求**

### **必需配置**
1. **API密钥**：Web服务API类型
2. **域名配置**：https://restapi.amap.com
3. **权限配置**：scope.userLocation

### **推荐配置**
1. **搜索范围**：50km（可根据需要调整）
2. **结果数量**：10个（平衡性能和选择）
3. **缓存策略**：启用搜索结果缓存

---

**文档版本**：1.0.0  
**更新时间**：2025年6月26日  
**参考**：高德地图Android导航SDK步行骑行路线规划
