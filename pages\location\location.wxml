<!--location.wxml-->
<view class="container">
  <view class="header">
    <text class="title">高德地图定位功能</text>
  </view>
  
  <!-- 定位信息显示区域 -->
  <view class="location-info" wx:if="{{locationData}}">
    <view class="info-item">
      <text class="label">当前位置：</text>
      <text class="value">{{locationData.name || '未知位置'}}</text>
    </view>
    <view class="info-item">
      <text class="label">详细地址：</text>
      <text class="value">{{locationData.desc || '获取中...'}}</text>
    </view>
    <view class="info-item">
      <text class="label">经度：</text>
      <text class="value">{{locationData.longitude}}</text>
    </view>
    <view class="info-item">
      <text class="label">纬度：</text>
      <text class="value">{{locationData.latitude}}</text>
    </view>
  </view>

  <!-- 地图显示区域 -->
  <view class="map-container">
    <map 
      id="myMap"
      class="map"
      longitude="{{longitude}}"
      latitude="{{latitude}}"
      scale="16"
      markers="{{markers}}"
      show-location="{{true}}"
      bindmarkertap="onMarkerTap">
    </map>
  </view>

  <!-- 操作按钮区域 -->
  <view class="button-group">
    <button class="btn primary" bindtap="getCurrentLocation">获取当前位置</button>
    <button class="btn secondary" bindtap="getWeatherInfo">获取天气信息</button>
    <button class="btn secondary" bindtap="searchNearbyPOI">搜索周边</button>
  </view>

  <!-- 天气信息显示 -->
  <view class="weather-info" wx:if="{{weatherData}}">
    <view class="weather-title">天气信息</view>
    <view class="weather-item">
      <text>城市：{{weatherData.city.data}}</text>
    </view>
    <view class="weather-item">
      <text>天气：{{weatherData.weather.data}}</text>
    </view>
    <view class="weather-item">
      <text>温度：{{weatherData.temperature.data}}°C</text>
    </view>
    <view class="weather-item">
      <text>风向：{{weatherData.winddirection.data}}</text>
    </view>
    <view class="weather-item">
      <text>风力：{{weatherData.windpower.data}}</text>
    </view>
    <view class="weather-item">
      <text>湿度：{{weatherData.humidity.data}}</text>
    </view>
  </view>

  <!-- 周边POI信息 -->
  <view class="poi-list" wx:if="{{poiData && poiData.length > 0}}">
    <view class="poi-title">周边信息</view>
    <scroll-view class="poi-scroll" scroll-y="true">
      <view class="poi-item" wx:for="{{poiData}}" wx:key="id" bindtap="onPOITap" data-index="{{index}}">
        <view class="poi-name">{{item.name}}</view>
        <view class="poi-address">{{item.address}}</view>
        <view class="poi-distance">距离：{{item.distance || '未知'}}米</view>
      </view>
    </scroll-view>
  </view>

  <!-- 错误信息显示 -->
  <view class="error-info" wx:if="{{errorMsg}}">
    <text class="error-text">{{errorMsg}}</text>
  </view>
</view>
