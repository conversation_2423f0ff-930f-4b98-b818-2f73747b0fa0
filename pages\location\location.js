// location.js
const amapManager = require('../../utils/amapManager.js'); // 引入SDK管理器
const CONFIG = require('../../utils/config.js'); // 引入配置文件
const errorHandler = require('../../utils/errorHandler.js'); // 引入错误处理工具
const { formatDistance, formatDuration } = require('../../utils/util.js'); // 引入工具函数

Page({
  data: {
    longitude: 116.397428, // 默认经度（北京天安门）
    latitude: 39.90923, // 默认纬度
    markers: [], // 地图标记点
    locationData: null, // 当前位置数据
    weatherData: null, // 天气数据
    poiData: [], // 周边POI数据
    errorMsg: '' // 错误信息
  },

  onLoad: function() {
    const that = this;

    // 使用统一的SDK管理器测试连接
    amapManager.testConnection()
      .then(result => {
        console.log('SDK连接测试成功:', result);
        
        // 页面加载时自动获取位置
        that.getCurrentLocation();
      })
      .catch(error => {
        console.error('SDK连接测试失败:', error);
        
        // 使用统一的错误处理
        errorHandler.handleApiError(error, {
          showModal: true,
          onCancel: () => that.onLoad() // 重试
        });
        
        that.setData({
          errorMsg: `地图服务连接失败: ${error.message || '请检查网络连接和API密钥'}`
        });
      });
  },

  // 获取当前位置
  getCurrentLocation: function() {
    const that = this;

    wx.showLoading({
      title: '定位中...'
    });

    // 使用SDK管理器安全调用getRegeo方法（自动处理权限检查）
    amapManager.safeCall('getRegeo')
      .then(result => {
        console.log('定位成功:', result);
        
        if (result.data && result.data.length > 0) {
          const locationInfo = result.data[0];
          
          // 同步更新所有位置相关数据
          that.setData({
            longitude: locationInfo.longitude,
            latitude: locationInfo.latitude,
            locationData: {
              name: locationInfo.name,
              desc: locationInfo.desc,
              longitude: locationInfo.longitude.toFixed(6),
              latitude: locationInfo.latitude.toFixed(6)
            },
            markers: [{
              id: 0,
              latitude: locationInfo.latitude,
              longitude: locationInfo.longitude,
              width: 30,
              height: 30,
              title: '当前位置'
            }],
            errorMsg: ''
          });
          
          wx.showToast({
            title: '定位成功',
            icon: 'success'
          });
        }
        
        wx.hideLoading();
      })
      .catch(error => {
        console.error('定位失败:', error);
        wx.hideLoading();
        
        // 使用统一的错误处理
        errorHandler.handleApiError(error, {
          showToast: true
        });
        
        that.setData({
          errorMsg: '定位失败: ' + (error.message || '未知错误')
        });
      });
  },

  // 获取天气信息
  getWeatherInfo: function() {
    const that = this;

    wx.showLoading({
      title: '获取天气中...'
    });

    // 使用SDK管理器安全调用getWeather方法
    amapManager.safeCall('getWeather')
      .then(result => {
        console.log('天气信息获取成功:', result);
        
        that.setData({
          weatherData: result.data,
          errorMsg: ''
        });
        
        wx.hideLoading();
        wx.showToast({
          title: '天气信息获取成功',
          icon: 'success'
        });
      })
      .catch(error => {
        console.error('天气信息获取失败:', error);
        wx.hideLoading();
        
        // 使用统一的错误处理
        errorHandler.handleApiError(error, {
          showToast: true
        });
        
        that.setData({
          errorMsg: '天气信息获取失败: ' + (error.message || '未知错误')
        });
      });
  },

  // 搜索周边POI
  searchNearbyPOI: function() {
    const that = this;

    // 检查位置数据是否有效（同时检查locationData和坐标）
    if (!that.data.locationData || !that.data.longitude || !that.data.latitude) {
      wx.showToast({
        title: '请先获取当前位置',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '搜索周边...'
    });

    // 使用SDK管理器安全调用getPoiAround方法
    amapManager.safeCall('getPoiAround', {
      location: `${that.data.longitude},${that.data.latitude}`,
      querytypes: '050000|060000|070000', // 餐饮、购物、生活服务
      querykeywords: '餐厅|商店|银行|医院'
    })
      .then(result => {
        console.log('周边POI搜索成功:', result);
        
        if (result.data && result.data.markers && result.data.poisData) {
          // 更新地图标记
          const allMarkers = [...that.data.markers, ...result.data.markers];
          
          that.setData({
            markers: allMarkers,
            poiData: result.data.poisData,
            errorMsg: ''
          });
          
          wx.showToast({
            title: `找到${result.data.poisData.length}个周边地点`,
            icon: 'success'
          });
        }
        
        wx.hideLoading();
      })
      .catch(error => {
        console.error('周边POI搜索失败:', error);
        wx.hideLoading();
        
        // 使用统一的错误处理
        errorHandler.handleApiError(error, {
          showToast: true
        });
        
        that.setData({
          errorMsg: '周边搜索失败: ' + (error.message || '未知错误')
        });
      });
  },

  // 地图标记点击事件
  onMarkerTap: function(e) {
    const markerId = e.detail.markerId;
    console.log('点击了标记:', markerId);
    
    if (markerId > 0 && this.data.poiData[markerId - 1]) {
      const poi = this.data.poiData[markerId - 1];
      wx.showModal({
        title: poi.name,
        content: `地址: ${poi.address}\n电话: ${poi.tel || '暂无'}`,
        showCancel: false
      });
    }
  },

  // POI列表项点击事件
  onPOITap: function(e) {
    const index = e.currentTarget.dataset.index;
    const poi = this.data.poiData[index];
    
    if (poi) {
      // 在地图上居中显示该POI
      const location = poi.location.split(',');
      this.setData({
        longitude: parseFloat(location[0]),
        latitude: parseFloat(location[1])
      });
      
      wx.showModal({
        title: poi.name,
        content: `地址: ${poi.address}\n电话: ${poi.tel || '暂无'}`,
        showCancel: false
      });
    }
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.getCurrentLocation();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // 页面分享
  onShareAppMessage: function() {
    return {
      title: '高德地图定位功能',
      path: '/pages/location/location'
    };
  },

  // 页面卸载时清理资源
  onUnload: function() {
    // 清理SDK实例
    amapManager.destroyInstance();
    console.log('定位页面资源清理完成');
  }
});
