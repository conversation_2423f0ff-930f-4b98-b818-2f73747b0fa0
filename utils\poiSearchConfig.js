// poiSearchConfig.js - POI搜索和地理编码配置
const CONFIG = require('./config.js');

// POI搜索配置
const POI_SEARCH_CONFIG = {
  BASE_URL: 'https://restapi.amap.com/v3/place/text',
  GEOCODING_URL: 'https://restapi.amap.com/v3/geocode/geo',
  REVERSE_GEOCODING_URL: 'https://restapi.amap.com/v3/geocode/regeo',
  
  // 默认搜索参数
  DEFAULTS: {
    city: '全国',
    output: 'json',
    offset: 10,
    page: 1,
    extensions: 'all'
  },
  
  // 常用POI类型
  POI_TYPES: {
    RESTAURANT: '餐饮服务',
    SHOPPING: '购物服务',
    TRANSPORT: '交通设施服务',
    HOTEL: '住宿服务',
    SCENIC: '风景名胜',
    HOSPITAL: '医疗保健服务',
    EDUCATION: '科教文化服务',
    FINANCE: '金融保险服务'
  }
};

// POI搜索
function searchPOI(keyword, options = {}) {
  return new Promise((resolve, reject) => {
    if (!keyword || !keyword.trim()) {
      reject({
        success: false,
        message: '搜索关键词不能为空'
      });
      return;
    }
    
    // 构建搜索参数
    const params = {
      key: CONFIG.AMAP_WEB_KEY, // 使用Web服务API密钥
      keywords: keyword.trim(),
      city: options.city || POI_SEARCH_CONFIG.DEFAULTS.city,
      output: POI_SEARCH_CONFIG.DEFAULTS.output,
      offset: options.offset || POI_SEARCH_CONFIG.DEFAULTS.offset,
      page: options.page || POI_SEARCH_CONFIG.DEFAULTS.page,
      extensions: POI_SEARCH_CONFIG.DEFAULTS.extensions
    };
    
    // 添加可选参数
    if (options.types) params.types = options.types;
    if (options.location) params.location = options.location;
    if (options.radius) params.radius = options.radius;
    if (options.sortrule) params.sortrule = options.sortrule;
    
    // 构建URL
    const queryString = Object.entries(params)
      .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
      .join('&');
    const url = `${POI_SEARCH_CONFIG.BASE_URL}?${queryString}`;
    
    console.log('POI搜索URL:', url);
    
    // 发送请求
    wx.request({
      url: url,
      method: 'GET',
      success: function(res) {
        console.log('POI搜索响应:', res);
        
        if (res.statusCode === 200) {
          const data = res.data;
          
          if (data.status === '1') {
            const results = parsePOIResults(data.pois || []);
            resolve({
              success: true,
              results: results,
              count: data.count || 0,
              suggestion: data.suggestion || null
            });
          } else {
            reject({
              success: false,
              message: data.info || 'POI搜索失败',
              code: data.infocode
            });
          }
        } else {
          reject({
            success: false,
            message: `HTTP错误: ${res.statusCode}`,
            code: res.statusCode
          });
        }
      },
      fail: function(error) {
        reject({
          success: false,
          message: '网络请求失败',
          error: error
        });
      }
    });
  });
}

// 解析POI搜索结果
function parsePOIResults(pois) {
  return pois.map((poi, index) => {
    const location = poi.location ? poi.location.split(',') : ['0', '0'];
    
    return {
      id: poi.id || index,
      name: poi.name || '未知地点',
      address: poi.address || '',
      type: poi.type || '',
      typecode: poi.typecode || '',
      longitude: parseFloat(location[0]) || 0,
      latitude: parseFloat(location[1]) || 0,
      distance: poi.distance ? parseInt(poi.distance) : null,
      tel: poi.tel || '',
      postcode: poi.postcode || '',
      website: poi.website || '',
      email: poi.email || '',
      pcode: poi.pcode || '',
      citycode: poi.citycode || '',
      adcode: poi.adcode || '',
      importance: poi.importance || '',
      shopinfo: poi.shopinfo || '',
      photos: poi.photos || []
    };
  });
}

// 地理编码（地址转坐标）
function geocoding(address, city = '') {
  return new Promise((resolve, reject) => {
    if (!address || !address.trim()) {
      reject({
        success: false,
        message: '地址不能为空'
      });
      return;
    }
    
    const params = {
      key: CONFIG.AMAP_WEB_KEY, // 使用Web服务API密钥
      address: address.trim(),
      output: 'json'
    };
    
    if (city) params.city = city;
    
    const queryString = Object.entries(params)
      .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
      .join('&');
    const url = `${POI_SEARCH_CONFIG.GEOCODING_URL}?${queryString}`;
    
    wx.request({
      url: url,
      method: 'GET',
      success: function(res) {
        if (res.statusCode === 200 && res.data.status === '1') {
          const geocodes = res.data.geocodes || [];
          if (geocodes.length > 0) {
            const result = geocodes[0];
            const location = result.location.split(',');
            
            resolve({
              success: true,
              longitude: parseFloat(location[0]),
              latitude: parseFloat(location[1]),
              formatted_address: result.formatted_address,
              level: result.level
            });
          } else {
            reject({
              success: false,
              message: '未找到该地址的坐标'
            });
          }
        } else {
          reject({
            success: false,
            message: res.data.info || '地理编码失败'
          });
        }
      },
      fail: function(error) {
        reject({
          success: false,
          message: '网络请求失败',
          error: error
        });
      }
    });
  });
}

// 逆地理编码（坐标转地址）
function reverseGeocoding(longitude, latitude, options = {}) {
  return new Promise((resolve, reject) => {
    if (!longitude || !latitude) {
      reject({
        success: false,
        message: '经纬度不能为空'
      });
      return;
    }
    
    const params = {
      key: CONFIG.AMAP_WEB_KEY, // 使用Web服务API密钥
      location: `${longitude},${latitude}`,
      output: 'json',
      radius: options.radius || 1000,
      extensions: options.extensions || 'base'
    };
    
    if (options.roadlevel !== undefined) params.roadlevel = options.roadlevel;
    if (options.homeorcorp !== undefined) params.homeorcorp = options.homeorcorp;
    
    const queryString = Object.entries(params)
      .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
      .join('&');
    const url = `${POI_SEARCH_CONFIG.REVERSE_GEOCODING_URL}?${queryString}`;
    
    wx.request({
      url: url,
      method: 'GET',
      success: function(res) {
        if (res.statusCode === 200 && res.data.status === '1') {
          const regeocode = res.data.regeocode;
          
          resolve({
            success: true,
            formatted_address: regeocode.formatted_address,
            addressComponent: regeocode.addressComponent,
            pois: regeocode.pois || [],
            roads: regeocode.roads || [],
            roadinters: regeocode.roadinters || [],
            aois: regeocode.aois || []
          });
        } else {
          reject({
            success: false,
            message: res.data.info || '逆地理编码失败'
          });
        }
      },
      fail: function(error) {
        reject({
          success: false,
          message: '网络请求失败',
          error: error
        });
      }
    });
  });
}

// 周边搜索
function searchNearby(longitude, latitude, keyword = '', options = {}) {
  const searchOptions = {
    ...options,
    location: `${longitude},${latitude}`,
    radius: options.radius || 3000, // 默认3km范围
    sortrule: 'distance' // 按距离排序
  };
  
  return searchPOI(keyword || '餐厅|商店|银行|医院', searchOptions);
}

module.exports = {
  POI_SEARCH_CONFIG,
  searchPOI,
  parsePOIResults,
  geocoding,
  reverseGeocoding,
  searchNearby
};
