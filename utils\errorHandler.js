// errorHandler.js - 统一错误处理工具
const CONFIG = require('./config.js');

// 错误类型映射
const ERROR_TYPES = {
  // API密钥相关错误
  USERKEY_PLAT_NOMATCH: {
    title: 'API密钥平台不匹配',
    message: '当前API密钥不适用于微信小程序平台',
    suggestions: [
      '请确认使用的是微信小程序专用API密钥',
      '访问高德开放平台重新申请密钥',
      '检查密钥配置是否正确'
    ]
  },
  
  INVALID_USER_KEY: {
    title: 'API密钥无效',
    message: '提供的API密钥无效或已过期',
    suggestions: [
      '检查API密钥是否输入正确',
      '确认密钥是否已激活',
      '联系管理员核实密钥状态'
    ]
  },
  
  DAILY_QUERY_OVER_LIMIT: {
    title: 'API调用超限',
    message: '今日API调用次数已达上限',
    suggestions: [
      '请明天再试',
      '联系管理员升级API配额',
      '优化应用减少API调用频率'
    ]
  },
  
  // 定位相关错误
  LOCATION_PERMISSION_DENIED: {
    title: '定位权限被拒绝',
    message: '无法获取位置信息，定位权限未授权',
    suggestions: [
      '请在设置中开启定位权限',
      '重新启动小程序并允许定位',
      '检查设备GPS是否开启'
    ]
  },
  
  LOCATION_UNAVAILABLE: {
    title: '定位服务不可用',
    message: '当前无法获取位置信息',
    suggestions: [
      '请确保设备GPS已开启',
      '尝试在室外环境使用',
      '检查网络连接状态',
      '稍后重试'
    ]
  },
  
  // 网络相关错误
  NETWORK_ERROR: {
    title: '网络连接失败',
    message: '无法连接到服务器',
    suggestions: [
      '检查网络连接状态',
      '尝试切换网络环境',
      '稍后重试'
    ]
  },
  
  // 数据相关错误
  DATA_PARSE_ERROR: {
    title: '数据解析失败',
    message: '服务器返回的数据格式异常',
    suggestions: [
      '请重试操作',
      '如果问题持续存在，请联系技术支持'
    ]
  },
  
  // 路线规划相关错误
  ROUTE_PLANNING_FAILED: {
    title: '路线规划失败',
    message: '无法规划指定路线',
    suggestions: [
      '检查起点和终点是否正确',
      '尝试选择其他路线类型',
      '确认起终点距离是否合理'
    ]
  }
};

/**
 * 解析错误信息
 * @param {Object} error 错误对象
 * @returns {Object} 解析后的错误信息
 */
function parseError(error) {
  let errorType = 'UNKNOWN_ERROR';
  let errorDetail = '';
  
  // 从error对象中提取错误信息
  if (error && error.errMsg) {
    errorDetail = error.errMsg;
    
    // 匹配已知错误类型
    for (const [key, value] of Object.entries(ERROR_TYPES)) {
      if (errorDetail.includes(key)) {
        errorType = key;
        break;
      }
    }
    
    // 特殊处理定位错误
    if (errorDetail.includes('getLocation:fail')) {
      if (errorDetail.includes('deny')) {
        errorType = 'LOCATION_PERMISSION_DENIED';
      } else {
        errorType = 'LOCATION_UNAVAILABLE';
      }
    }
    
    // 特殊处理网络错误
    if (errorDetail.includes('request:fail') || errorDetail.includes('timeout')) {
      errorType = 'NETWORK_ERROR';
    }
  } else if (error && error.message) {
    errorDetail = error.message;
  } else if (typeof error === 'string') {
    errorDetail = error;
  }
  
  // 获取错误配置
  const errorConfig = ERROR_TYPES[errorType] || {
    title: '未知错误',
    message: errorDetail || '发生了未知错误',
    suggestions: ['请重试操作', '如果问题持续存在，请联系技术支持']
  };
  
  return {
    type: errorType,
    title: errorConfig.title,
    message: errorConfig.message,
    suggestions: errorConfig.suggestions,
    originalError: error,
    detail: errorDetail
  };
}

/**
 * 显示用户友好的错误提示
 * @param {Object} error 错误对象
 * @param {Object} options 显示选项
 */
function showError(error, options = {}) {
  const parsedError = parseError(error);
  
  const {
    showSuggestions = true,
    showCancel = true,
    confirmText = '我知道了',
    cancelText = '重试'
  } = options;
  
  // 构建提示内容
  let content = parsedError.message;
  
  if (showSuggestions && parsedError.suggestions.length > 0) {
    content += '\n\n解决建议：\n';
    parsedError.suggestions.forEach((suggestion, index) => {
      content += `${index + 1}. ${suggestion}\n`;
    });
  }
  
  // 显示模态框
  wx.showModal({
    title: parsedError.title,
    content: content.trim(),
    showCancel: showCancel,
    confirmText: confirmText,
    cancelText: cancelText,
    success: function(res) {
      if (options.onConfirm && res.confirm) {
        options.onConfirm(parsedError);
      }
      if (options.onCancel && res.cancel) {
        options.onCancel(parsedError);
      }
    }
  });
  
  // 记录错误日志
  console.error('错误处理:', parsedError);
  
  return parsedError;
}

/**
 * 显示简单的错误Toast
 * @param {Object} error 错误对象
 * @param {string} duration 显示时长
 */
function showErrorToast(error, duration = 3000) {
  const parsedError = parseError(error);
  
  wx.showToast({
    title: parsedError.message,
    icon: 'none',
    duration: duration
  });
  
  console.error('错误Toast:', parsedError);
  
  return parsedError;
}

/**
 * 处理API调用错误
 * @param {Object} error API错误对象
 * @param {Object} options 处理选项
 */
function handleApiError(error, options = {}) {
  const {
    showModal = true,
    showToast = false,
    autoRetry = false,
    retryFunction = null,
    maxRetry = 3
  } = options;
  
  const parsedError = parseError(error);
  
  // 根据错误类型决定处理方式
  if (autoRetry && retryFunction && parsedError.type === 'NETWORK_ERROR') {
    const retryCount = options.retryCount || 0;
    
    if (retryCount < maxRetry) {
      console.log(`网络错误，自动重试第${retryCount + 1}次`);
      setTimeout(() => {
        retryFunction({ ...options, retryCount: retryCount + 1 });
      }, 1000 * (retryCount + 1)); // 递增延迟重试
      return parsedError;
    }
  }
  
  // 显示错误信息
  if (showModal) {
    showError(error, {
      ...options,
      onCancel: retryFunction ? () => {
        if (retryFunction) retryFunction(options);
      } : undefined
    });
  } else if (showToast) {
    showErrorToast(error);
  }
  
  return parsedError;
}

module.exports = {
  parseError,
  showError,
  showErrorToast,
  handleApiError,
  ERROR_TYPES
}; 