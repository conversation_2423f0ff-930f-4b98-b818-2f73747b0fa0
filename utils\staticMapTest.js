// staticMapTest.js - 静态地图测试工具
const CONFIG = require('./config.js');

// 直接构建静态地图URL（严格按照官方文档规范）
function buildStaticMapUrl(options = {}) {
  const baseUrl = 'https://restapi.amap.com/v3/staticmap';

  // 必填参数
  const requiredParams = {
    key: CONFIG.AMAP_WEB_KEY, // 使用Web服务API密钥
    zoom: options.zoom || 10,  // 地图级别 [1,17]
    size: options.size || '400*400'  // 图片大小，默认400*400
  };

  // 可选参数
  const optionalParams = {};

  // location参数（中心点坐标）
  if (options.location) {
    optionalParams.location = options.location;
  }

  // scale参数（普通/高清）
  if (options.scale) {
    optionalParams.scale = options.scale;
  }

  // markers参数（标注）
  if (options.markers) {
    optionalParams.markers = options.markers;
  }

  // labels参数（标签）
  if (options.labels) {
    optionalParams.labels = options.labels;
  }

  // paths参数（折线）
  if (options.paths) {
    optionalParams.paths = options.paths;
  }

  // traffic参数（交通路况）
  if (options.traffic !== undefined) {
    optionalParams.traffic = options.traffic;
  }

  // 合并所有参数
  const allParams = { ...requiredParams, ...optionalParams };

  // 构建查询字符串
  const queryString = Object.entries(allParams)
    .filter(([key, value]) => value !== '' && value !== null && value !== undefined)
    .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
    .join('&');

  return `${baseUrl}?${queryString}`;
}

// 测试静态地图URL是否有效
function testStaticMapUrl(url) {
  return new Promise((resolve, reject) => {
    wx.request({
      url: url,
      method: 'GET',
      success: function(res) {
        if (res.statusCode === 200) {
          resolve({
            success: true,
            message: '静态地图URL有效',
            url: url,
            contentType: res.header['content-type'] || res.header['Content-Type']
          });
        } else {
          reject({
            success: false,
            message: `HTTP错误: ${res.statusCode}`,
            url: url
          });
        }
      },
      fail: function(error) {
        reject({
          success: false,
          message: '网络请求失败',
          error: error,
          url: url
        });
      }
    });
  });
}

// 获取当前位置的静态地图
function getCurrentLocationStaticMap() {
  return new Promise((resolve, reject) => {
    // 先获取当前位置
    wx.getLocation({
      type: 'gcj02',
      success: function(location) {
        const longitude = location.longitude;
        const latitude = location.latitude;
        const locationStr = `${longitude},${latitude}`;
        
        // 构建静态地图URL
        const mapUrl = buildStaticMapUrl({
          location: locationStr,
          markers: `mid,0xFF0000,A:${locationStr}`,
          zoom: 16
        });
        
        console.log('生成的静态地图URL:', mapUrl);
        
        // 测试URL是否有效
        testStaticMapUrl(mapUrl)
          .then(result => {
            resolve({
              success: true,
              mapUrl: mapUrl,
              location: {
                longitude: longitude,
                latitude: latitude
              },
              message: '静态地图生成成功'
            });
          })
          .catch(error => {
            reject({
              success: false,
              message: '静态地图URL测试失败',
              error: error,
              mapUrl: mapUrl
            });
          });
      },
      fail: function(error) {
        // 如果定位失败，使用默认位置
        const defaultLocation = '116.397428,39.90923';
        const mapUrl = buildStaticMapUrl({
          location: defaultLocation,
          markers: `mid,0xFF0000,A:${defaultLocation}`,
          zoom: 15
        });
        
        console.log('使用默认位置生成静态地图URL:', mapUrl);
        
        resolve({
          success: true,
          mapUrl: mapUrl,
          location: {
            longitude: 116.397428,
            latitude: 39.90923
          },
          message: '使用默认位置生成静态地图',
          isDefault: true
        });
      }
    });
  });
}

module.exports = {
  buildStaticMapUrl,
  testStaticMapUrl,
  getCurrentLocationStaticMap
};
